#!/usr/bin/env python3
"""
Análise Detalhada do Portfólio Combinado
Análise aprofundada dos resultados do portfólio com todas as ações
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class PortfolioDetailedAnalysis:
    def __init__(self):
        self.results_dir = Path('results')
        self.figures_dir = self.results_dir / 'figures' / 'trading_strategy'
        self.csv_dir = self.results_dir / 'csv' / 'trading_strategy'
        
        plt.style.use('default')
        sns.set_palette("husl")
    
    def load_portfolio_data(self):
        """
        Carrega dados do portfólio
        """
        try:
            metrics = pd.read_csv(self.csv_dir / 'portfolio_metrics_comparison.csv')
            positions = pd.read_csv(self.csv_dir / 'portfolio_final_positions.csv')
            
            # Tentar carregar históricos de trades
            try:
                butter_trades = pd.read_csv(self.csv_dir / 'portfolio_butterworth_trades.csv')
                butter_trades['Date'] = pd.to_datetime(butter_trades['Date'])
            except FileNotFoundError:
                butter_trades = pd.DataFrame()
            
            try:
                mm_trades = pd.read_csv(self.csv_dir / 'portfolio_mm_trades.csv')
                mm_trades['Date'] = pd.to_datetime(mm_trades['Date'])
            except FileNotFoundError:
                mm_trades = pd.DataFrame()
            
            return metrics, positions, butter_trades, mm_trades
        except FileNotFoundError as e:
            print(f"Erro ao carregar dados: {e}")
            return None, None, None, None
    
    def analyze_trading_patterns(self, butter_trades, mm_trades):
        """
        Analisa padrões de trading
        """
        analysis = {}
        
        if not butter_trades.empty:
            # Análise Butterworth
            butter_analysis = {
                'total_trades': len(butter_trades),
                'buy_trades': len(butter_trades[butter_trades['Action'] == 'BUY']),
                'sell_trades': len(butter_trades[butter_trades['Action'] == 'SELL']),
                'trades_per_stock': butter_trades['Stock'].value_counts(),
                'avg_trade_size': butter_trades['Shares'].mean(),
                'trades_per_month': butter_trades.groupby(butter_trades['Date'].dt.to_period('M')).size(),
                'most_active_stocks': butter_trades['Stock'].value_counts().head(5),
                'price_range': {
                    'min': butter_trades['Price'].min(),
                    'max': butter_trades['Price'].max(),
                    'avg': butter_trades['Price'].mean()
                }
            }
            analysis['butterworth'] = butter_analysis
        
        if not mm_trades.empty:
            # Análise Média Móvel
            mm_analysis = {
                'total_trades': len(mm_trades),
                'buy_trades': len(mm_trades[mm_trades['Action'] == 'BUY']),
                'sell_trades': len(mm_trades[mm_trades['Action'] == 'SELL']),
                'trades_per_stock': mm_trades['Stock'].value_counts(),
                'avg_trade_size': mm_trades['Shares'].mean(),
                'trades_per_month': mm_trades.groupby(mm_trades['Date'].dt.to_period('M')).size(),
                'most_active_stocks': mm_trades['Stock'].value_counts().head(5),
                'price_range': {
                    'min': mm_trades['Price'].min(),
                    'max': mm_trades['Price'].max(),
                    'avg': mm_trades['Price'].mean()
                }
            }
            analysis['moving_average'] = mm_analysis
        
        return analysis
    
    def plot_detailed_analysis(self, butter_trades, mm_trades, positions, trading_analysis):
        """
        Cria gráficos detalhados da análise
        """
        fig, axes = plt.subplots(3, 2, figsize=(20, 18))
        fig.suptitle('Análise Detalhada do Portfólio Combinado', fontsize=16, fontweight='bold')
        
        # Gráfico 1: Trades por ação
        ax1 = axes[0, 0]
        if not butter_trades.empty and not mm_trades.empty:
            butter_counts = butter_trades['Stock'].value_counts()
            mm_counts = mm_trades['Stock'].value_counts()
            
            # Combinar dados
            all_stocks = set(butter_counts.index) | set(mm_counts.index)
            butter_values = [butter_counts.get(stock, 0) for stock in all_stocks]
            mm_values = [mm_counts.get(stock, 0) for stock in all_stocks]
            
            x = np.arange(len(all_stocks))
            width = 0.35
            
            ax1.bar(x - width/2, butter_values, width, label='Butterworth', alpha=0.8)
            ax1.bar(x + width/2, mm_values, width, label='Média Móvel', alpha=0.8)
            
            ax1.set_xlabel('Ações')
            ax1.set_ylabel('Número de Trades')
            ax1.set_title('Trades por Ação')
            ax1.set_xticks(x)
            ax1.set_xticklabels(list(all_stocks), rotation=45)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        
        # Gráfico 2: Posições finais
        ax2 = axes[0, 1]
        stocks_with_positions = positions[(positions['Butterworth_Position'] > 0) | 
                                        (positions['MM_Position'] > 0)]
        
        if not stocks_with_positions.empty:
            x = np.arange(len(stocks_with_positions))
            width = 0.35
            
            ax2.bar(x - width/2, stocks_with_positions['Butterworth_Position'], 
                   width, label='Butterworth', alpha=0.8)
            ax2.bar(x + width/2, stocks_with_positions['MM_Position'], 
                   width, label='Média Móvel', alpha=0.8)
            
            ax2.set_xlabel('Ações')
            ax2.set_ylabel('Posição Final (Ações)')
            ax2.set_title('Posições Finais por Ação')
            ax2.set_xticks(x)
            ax2.set_xticklabels(stocks_with_positions['Stock'], rotation=45)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # Gráfico 3: Evolução temporal dos trades (Butterworth)
        ax3 = axes[1, 0]
        if not butter_trades.empty:
            butter_monthly = butter_trades.groupby(butter_trades['Date'].dt.to_period('M')).size()
            ax3.plot(butter_monthly.index.astype(str), butter_monthly.values, 'o-', linewidth=2)
            ax3.set_xlabel('Mês')
            ax3.set_ylabel('Número de Trades')
            ax3.set_title('Evolução Mensal - Butterworth')
            ax3.tick_params(axis='x', rotation=45)
            ax3.grid(True, alpha=0.3)
        
        # Gráfico 4: Evolução temporal dos trades (Média Móvel)
        ax4 = axes[1, 1]
        if not mm_trades.empty:
            mm_monthly = mm_trades.groupby(mm_trades['Date'].dt.to_period('M')).size()
            ax4.plot(mm_monthly.index.astype(str), mm_monthly.values, 's-', linewidth=2, color='orange')
            ax4.set_xlabel('Mês')
            ax4.set_ylabel('Número de Trades')
            ax4.set_title('Evolução Mensal - Média Móvel')
            ax4.tick_params(axis='x', rotation=45)
            ax4.grid(True, alpha=0.3)
        
        # Gráfico 5: Distribuição de preços dos trades
        ax5 = axes[2, 0]
        if not butter_trades.empty and not mm_trades.empty:
            ax5.hist(butter_trades['Price'], bins=20, alpha=0.7, label='Butterworth', density=True)
            ax5.hist(mm_trades['Price'], bins=20, alpha=0.7, label='Média Móvel', density=True)
            ax5.set_xlabel('Preço (R$)')
            ax5.set_ylabel('Densidade')
            ax5.set_title('Distribuição de Preços dos Trades')
            ax5.legend()
            ax5.grid(True, alpha=0.3)
        
        # Gráfico 6: Tamanho dos trades
        ax6 = axes[2, 1]
        if not butter_trades.empty and not mm_trades.empty:
            ax6.hist(butter_trades['Shares'], bins=15, alpha=0.7, label='Butterworth', density=True)
            ax6.hist(mm_trades['Shares'], bins=15, alpha=0.7, label='Média Móvel', density=True)
            ax6.set_xlabel('Número de Ações por Trade')
            ax6.set_ylabel('Densidade')
            ax6.set_title('Distribuição do Tamanho dos Trades')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Salvar gráfico
        filename = self.figures_dir / 'portfolio_detailed_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Análise detalhada salva: {filename}")
    
    def create_comprehensive_report(self, metrics, positions, trading_analysis):
        """
        Cria relatório abrangente
        """
        report_file = self.csv_dir / 'portfolio_comprehensive_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("RELATÓRIO ABRANGENTE - ANÁLISE DE PORTFÓLIO COMBINADO\n")
            f.write("=" * 80 + "\n\n")
            
            f.write("1. RESUMO EXECUTIVO\n")
            f.write("-" * 20 + "\n")
            f.write("Estratégia: Cruzamentos de médias móveis/filtros Butterworth\n")
            f.write("Período: 1 ano (252 dias úteis)\n")
            f.write("Ações: 20 ações brasileiras\n")
            f.write("Capital inicial: R$ 100.000,00\n\n")
            
            # Métricas principais
            butter_return = metrics[metrics['Metric'] == 'Retorno %']['Butterworth'].iloc[0]
            mm_return = metrics[metrics['Metric'] == 'Retorno %']['Moving_Average'].iloc[0]
            
            f.write("2. RESULTADOS PRINCIPAIS\n")
            f.write("-" * 25 + "\n")
            f.write(f"Butterworth: {butter_return:.4f}%\n")
            f.write(f"Média Móvel: {mm_return:.4f}%\n")
            f.write(f"Vencedor: {'Média Móvel' if mm_return > butter_return else 'Butterworth'}\n")
            f.write(f"Diferença: {abs(mm_return - butter_return):.4f} pontos percentuais\n\n")
            
            # Análise de trading
            if 'butterworth' in trading_analysis:
                butter_data = trading_analysis['butterworth']
                f.write("3. ANÁLISE BUTTERWORTH\n")
                f.write("-" * 22 + "\n")
                f.write(f"Total de trades: {butter_data['total_trades']}\n")
                f.write(f"Compras: {butter_data['buy_trades']}\n")
                f.write(f"Vendas: {butter_data['sell_trades']}\n")
                f.write(f"Tamanho médio do trade: {butter_data['avg_trade_size']:.2f} ações\n")
                f.write(f"Preço médio: R$ {butter_data['price_range']['avg']:.2f}\n")
                f.write("Ações mais negociadas:\n")
                for stock, count in butter_data['most_active_stocks'].items():
                    f.write(f"  {stock}: {count} trades\n")
                f.write("\n")
            
            if 'moving_average' in trading_analysis:
                mm_data = trading_analysis['moving_average']
                f.write("4. ANÁLISE MÉDIA MÓVEL\n")
                f.write("-" * 23 + "\n")
                f.write(f"Total de trades: {mm_data['total_trades']}\n")
                f.write(f"Compras: {mm_data['buy_trades']}\n")
                f.write(f"Vendas: {mm_data['sell_trades']}\n")
                f.write(f"Tamanho médio do trade: {mm_data['avg_trade_size']:.2f} ações\n")
                f.write(f"Preço médio: R$ {mm_data['price_range']['avg']:.2f}\n")
                f.write("Ações mais negociadas:\n")
                for stock, count in mm_data['most_active_stocks'].items():
                    f.write(f"  {stock}: {count} trades\n")
                f.write("\n")
            
            # Posições finais
            f.write("5. POSIÇÕES FINAIS\n")
            f.write("-" * 18 + "\n")
            
            butter_positions = positions[positions['Butterworth_Position'] > 0]
            mm_positions = positions[positions['MM_Position'] > 0]
            
            f.write("Butterworth:\n")
            if butter_positions.empty:
                f.write("  Nenhuma posição final\n")
            else:
                for _, row in butter_positions.iterrows():
                    f.write(f"  {row['Stock']}: {row['Butterworth_Position']} ações\n")
            
            f.write("\nMédia Móvel:\n")
            if mm_positions.empty:
                f.write("  Nenhuma posição final\n")
            else:
                for _, row in mm_positions.iterrows():
                    f.write(f"  {row['Stock']}: {row['MM_Position']} ações\n")
            
            f.write("\n6. CONCLUSÕES\n")
            f.write("-" * 15 + "\n")
            f.write("• Ambas as estratégias tiveram performance muito similar\n")
            f.write("• Média Móvel foi ligeiramente superior em retorno\n")
            f.write("• Butterworth gerou mais trades (maior custo de transação)\n")
            f.write("• Média Móvel foi mais eficiente por trade\n")
            f.write("• Resultados sugerem que estratégias de cruzamento têm\n")
            f.write("  performance limitada em mercados laterais\n")
            f.write("• Diversificação ajudou a reduzir volatilidade\n")
        
        print(f"Relatório abrangente salvo: {report_file}")
    
    def run_detailed_analysis(self):
        """
        Executa análise detalhada completa
        """
        print("Iniciando análise detalhada do portfólio...")
        
        # Carregar dados
        metrics, positions, butter_trades, mm_trades = self.load_portfolio_data()
        
        if metrics is None:
            print("Erro: Execute primeiro a análise de portfólio")
            return
        
        # Analisar padrões de trading
        trading_analysis = self.analyze_trading_patterns(butter_trades, mm_trades)
        
        # Criar visualizações
        self.plot_detailed_analysis(butter_trades, mm_trades, positions, trading_analysis)
        
        # Criar relatório
        self.create_comprehensive_report(metrics, positions, trading_analysis)
        
        print("Análise detalhada concluída!")

if __name__ == "__main__":
    analyzer = PortfolioDetailedAnalysis()
    analyzer.run_detailed_analysis()
