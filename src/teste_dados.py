#!/usr/bin/env python3
"""
Script para testar se os dados estão sendo filtrados corretamente
"""

import pandas as pd
import yfinance as yf
from datetime import datetime

def testar_filtragem():
    """Testa se a filtragem de dados está funcionando"""
    
    # Ler carteira
    carteira = pd.read_csv('carteira.csv')
    carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
    
    data_inicio = carteira['data_compra'].min()
    print(f"Data de início dos investimentos: {data_inicio.strftime('%d/%m/%Y')}")
    
    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        data_compra = posicao['data_compra']
        
        print(f"\n--- {ticker} ---")
        print(f"Data da compra: {data_compra.strftime('%d/%m/%Y')}")
        
        # Obter dados
        stock = yf.Ticker(ticker)
        hist_completo = stock.history(period='1mo')
        
        print(f"Dados completos: {hist_completo.index.min().strftime('%d/%m/%Y')} a {hist_completo.index.max().strftime('%d/%m/%Y')}")
        print(f"Total de dias: {len(hist_completo)}")
        
        # Filtrar dados
        hist_filtrado = hist_completo[hist_completo.index.date >= data_compra.date()]
        
        if not hist_filtrado.empty:
            print(f"Dados filtrados: {hist_filtrado.index.min().strftime('%d/%m/%Y')} a {hist_filtrado.index.max().strftime('%d/%m/%Y')}")
            print(f"Dias filtrados: {len(hist_filtrado)}")
            
            # Mostrar as primeiras datas
            print("Primeiras 3 datas dos dados filtrados:")
            for i, data in enumerate(hist_filtrado.index[:3]):
                print(f"  {i+1}. {data.strftime('%d/%m/%Y %H:%M')}")
        else:
            print("Nenhum dado após filtragem!")

if __name__ == "__main__":
    testar_filtragem()
