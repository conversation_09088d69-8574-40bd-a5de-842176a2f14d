#!/usr/bin/env python3
"""
Script para visualizar os gráficos da análise de carteira
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import os

def visualizar_graficos():
    """Exibe os gráficos gerados pela análise de carteira"""
    
    # Verificar se os arquivos existem
    grafico_individual = 'results/figures/rendimento_individual.png'
    grafico_carteira = 'results/figures/carteira_total.png'
    
    graficos_existem = []
    if os.path.exists(grafico_individual):
        graficos_existem.append(('Rendimento Individual', grafico_individual))
    if os.path.exists(grafico_carteira):
        graficos_existem.append(('Carteira Total', grafico_carteira))
    
    if not graficos_existem:
        print("Nenhum gráfico encontrado. Execute primeiro o script de análise da carteira.")
        return
    
    # Criar figura para exibir os gráficos
    n_graficos = len(graficos_existem)
    fig, axes = plt.subplots(n_graficos, 1, figsize=(15, 8*n_graficos))
    
    if n_graficos == 1:
        axes = [axes]
    
    for i, (titulo, caminho) in enumerate(graficos_existem):
        img = mpimg.imread(caminho)
        axes[i].imshow(img)
        axes[i].set_title(titulo, fontsize=16, fontweight='bold')
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig('results/figures/visualizacao_completa.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Gráficos visualizados e salvos em 'results/figures/visualizacao_completa.png'")

if __name__ == "__main__":
    visualizar_graficos()
