#!/usr/bin/env python3
"""
Análise de Estratégia de Trading baseada em Cruzamentos
Compara estratégias usando dados de Butterworth vs Médias Móveis

Regras:
- Comprar 1 ação quando linha de 10 dias cruza de cima para baixo qualquer outra linha
- Vender 1 ação quando linha de 10 dias cruza de baixo para cima qualquer outra linha
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class TradingStrategyAnalyzer:
    def __init__(self):
        self.results_dir = Path('results')
        self.figures_dir = self.results_dir / 'figures' / 'trading_strategy'
        self.csv_dir = self.results_dir / 'csv' / 'trading_strategy'
        
        # Criar diretórios se não existirem
        self.figures_dir.mkdir(parents=True, exist_ok=True)
        self.csv_dir.mkdir(parents=True, exist_ok=True)
        
        # Configurar estilo dos gráficos
        plt.style.use('default')
        sns.set_palette("husl")
    
    def detect_crossings(self, df, signal_10_col, other_signal_cols):
        """
        Detecta cruzamentos da linha de 10 dias com outras linhas
        Retorna DataFrame com sinais de compra/venda
        """
        signals = pd.DataFrame(index=df.index)
        signals['Date'] = df['Data']
        signals['Price'] = df['Preco_Media_OHLC']
        signals['Signal_10'] = df[signal_10_col]
        
        # Inicializar colunas de sinais
        signals['Buy_Signal'] = 0
        signals['Sell_Signal'] = 0
        signals['Position'] = 0
        signals['Shares'] = 0
        
        for other_col in other_signal_cols:
            signals[f'Other_{other_col}'] = df[other_col]
            
            # Detectar cruzamentos
            # Cruzamento de baixo para cima (10 dias estava abaixo, agora está acima) = COMPRA (sinal de alta)
            cross_up = ((signals['Signal_10'].shift(1) < signals[f'Other_{other_col}'].shift(1)) &
                       (signals['Signal_10'] > signals[f'Other_{other_col}']))

            # Cruzamento de cima para baixo (10 dias estava acima, agora está abaixo) = VENDA (sinal de baixa)
            cross_down = ((signals['Signal_10'].shift(1) > signals[f'Other_{other_col}'].shift(1)) &
                         (signals['Signal_10'] < signals[f'Other_{other_col}']))

            # Somar sinais (cada cruzamento = +1 ação comprada/vendida)
            signals['Buy_Signal'] += cross_up.astype(int)
            signals['Sell_Signal'] += cross_down.astype(int)
        
        # Calcular posição acumulada
        signals['Net_Signal'] = signals['Buy_Signal'] - signals['Sell_Signal']
        signals['Position'] = signals['Net_Signal'].cumsum()
        
        return signals
    
    def calculate_performance(self, signals):
        """
        Calcula performance da estratégia
        """
        # Calcular valor do portfólio
        signals['Portfolio_Value'] = signals['Position'] * signals['Price']
        
        # Calcular investimento acumulado (assumindo compra ao preço atual)
        signals['Cumulative_Investment'] = 0
        cumulative_inv = 0
        
        for i in range(len(signals)):
            if signals['Buy_Signal'].iloc[i] > 0:
                cumulative_inv += signals['Buy_Signal'].iloc[i] * signals['Price'].iloc[i]
            if signals['Sell_Signal'].iloc[i] > 0:
                cumulative_inv -= signals['Sell_Signal'].iloc[i] * signals['Price'].iloc[i]
            signals['Cumulative_Investment'].iloc[i] = cumulative_inv
        
        # Calcular retorno
        signals['Total_Value'] = signals['Portfolio_Value'] + signals['Cumulative_Investment']
        
        # Métricas de performance
        total_trades = (signals['Buy_Signal'] + signals['Sell_Signal']).sum()
        final_position = signals['Position'].iloc[-1]
        final_investment = signals['Cumulative_Investment'].iloc[-1]
        final_portfolio_value = signals['Portfolio_Value'].iloc[-1]
        total_return = final_portfolio_value - final_investment if final_investment != 0 else 0
        
        return {
            'total_trades': total_trades,
            'final_position': final_position,
            'final_investment': final_investment,
            'final_portfolio_value': final_portfolio_value,
            'total_return': total_return,
            'return_percentage': (total_return / abs(final_investment) * 100) if final_investment != 0 else 0
        }
    
    def analyze_stock(self, stock_symbol):
        """
        Analisa uma ação específica para ambas as estratégias
        """
        # Carregar dados
        butterworth_file = f'results/csv/butterworth_analysis/individual_stocks/butterworth_series_{stock_symbol}.csv'
        mm_file = f'results/csv/mm_analysis/individual_stocks/mm_series_{stock_symbol}.csv'
        
        try:
            df_butter = pd.read_csv(butterworth_file)
            df_mm = pd.read_csv(mm_file)
        except FileNotFoundError as e:
            print(f"Arquivo não encontrado para {stock_symbol}: {e}")
            return None
        
        # Filtrar para 1 ano de dados (últimos 252 dias úteis aproximadamente)
        df_butter = df_butter.tail(252)
        df_mm = df_mm.tail(252)
        
        # Definir colunas de sinais
        butter_cols = ['Butterworth_25_dias', 'Butterworth_50_dias', 'Butterworth_100_dias', 'Butterworth_200_dias']
        mm_cols = ['MM25_dias', 'MM50_dias', 'MM100_dias', 'MM200_dias']
        
        # Analisar estratégias
        signals_butter = self.detect_crossings(df_butter, 'Butterworth_10_dias', butter_cols)
        signals_mm = self.detect_crossings(df_mm, 'MM10_dias', mm_cols)
        
        # Calcular performance
        perf_butter = self.calculate_performance(signals_butter)
        perf_mm = self.calculate_performance(signals_mm)
        
        return {
            'stock': stock_symbol,
            'butterworth': {
                'signals': signals_butter,
                'performance': perf_butter
            },
            'moving_average': {
                'signals': signals_mm,
                'performance': perf_mm
            }
        }
    
    def plot_strategy_comparison(self, analysis_result):
        """
        Plota comparação das estratégias para uma ação
        """
        stock = analysis_result['stock']
        signals_butter = analysis_result['butterworth']['signals']
        signals_mm = analysis_result['moving_average']['signals']
        
        fig, axes = plt.subplots(3, 2, figsize=(20, 15))
        fig.suptitle(f'Análise de Estratégia de Trading - {stock}', fontsize=16, fontweight='bold')
        
        # Converter datas
        signals_butter['Date'] = pd.to_datetime(signals_butter['Date'])
        signals_mm['Date'] = pd.to_datetime(signals_mm['Date'])
        
        # Gráfico 1: Preço e Sinais - Butterworth
        ax1 = axes[0, 0]
        ax1.plot(signals_butter['Date'], signals_butter['Price'], label='Preço', linewidth=2)
        ax1.plot(signals_butter['Date'], signals_butter['Signal_10'], label='Butterworth 10d', linewidth=1.5)
        
        # Marcar pontos de compra e venda
        buy_points = signals_butter[signals_butter['Buy_Signal'] > 0]
        sell_points = signals_butter[signals_butter['Sell_Signal'] > 0]
        
        ax1.scatter(buy_points['Date'], buy_points['Price'], color='green', marker='^', s=100, label='Compra', zorder=5)
        ax1.scatter(sell_points['Date'], sell_points['Price'], color='red', marker='v', s=100, label='Venda', zorder=5)
        
        ax1.set_title('Estratégia Butterworth')
        ax1.set_ylabel('Preço (R$)')
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # Gráfico 2: Preço e Sinais - Média Móvel
        ax2 = axes[0, 1]
        ax2.plot(signals_mm['Date'], signals_mm['Price'], label='Preço', linewidth=2)
        ax2.plot(signals_mm['Date'], signals_mm['Signal_10'], label='MM 10d', linewidth=1.5)
        
        buy_points_mm = signals_mm[signals_mm['Buy_Signal'] > 0]
        sell_points_mm = signals_mm[signals_mm['Sell_Signal'] > 0]
        
        ax2.scatter(buy_points_mm['Date'], buy_points_mm['Price'], color='green', marker='^', s=100, label='Compra', zorder=5)
        ax2.scatter(sell_points_mm['Date'], sell_points_mm['Price'], color='red', marker='v', s=100, label='Venda', zorder=5)
        
        ax2.set_title('Estratégia Média Móvel')
        ax2.set_ylabel('Preço (R$)')
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        # Gráfico 3: Posição ao longo do tempo
        ax3 = axes[1, 0]
        ax3.plot(signals_butter['Date'], signals_butter['Position'], label='Butterworth', linewidth=2)
        ax3.set_title('Posição em Ações - Butterworth')
        ax3.set_ylabel('Número de Ações')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        ax4 = axes[1, 1]
        ax4.plot(signals_mm['Date'], signals_mm['Position'], label='Média Móvel', linewidth=2, color='orange')
        ax4.set_title('Posição em Ações - Média Móvel')
        ax4.set_ylabel('Número de Ações')
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # Gráfico 5: Valor do Portfólio
        ax5 = axes[2, 0]
        ax5.plot(signals_butter['Date'], signals_butter['Portfolio_Value'], label='Butterworth', linewidth=2)
        ax5.set_title('Valor do Portfólio - Butterworth')
        ax5.set_ylabel('Valor (R$)')
        ax5.set_xlabel('Data')
        ax5.grid(True, alpha=0.3)
        ax5.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        ax6 = axes[2, 1]
        ax6.plot(signals_mm['Date'], signals_mm['Portfolio_Value'], label='Média Móvel', linewidth=2, color='orange')
        ax6.set_title('Valor do Portfólio - Média Móvel')
        ax6.set_ylabel('Valor (R$)')
        ax6.set_xlabel('Data')
        ax6.grid(True, alpha=0.3)
        ax6.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        
        # Salvar gráfico
        filename = self.figures_dir / f'trading_strategy_{stock}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Gráfico salvo: {filename}")
    
    def run_analysis(self):
        """
        Executa análise completa para todas as ações
        """
        # Lista de ações (baseada nos arquivos disponíveis)
        stocks = ['ABEV3', 'AZUL4', 'BBAS3', 'BEEF3', 'BRFS3', 'CPFE3', 'EMBR3', 
                 'GGBR4', 'GOLL4', 'HYPE3', 'JBSS3', 'KLBN11', 'MRFG3', 'PCAR3', 
                 'PETR3', 'RADL3', 'SUZB3', 'TIMS3', 'VALE3', 'WEGE3']
        
        results = []
        
        print("Iniciando análise de estratégias de trading...")
        print("=" * 60)
        
        for stock in stocks:
            print(f"Analisando {stock}...")
            
            analysis = self.analyze_stock(stock)
            if analysis:
                results.append(analysis)
                
                # Plotar comparação
                self.plot_strategy_comparison(analysis)
                
                # Mostrar resultados
                butter_perf = analysis['butterworth']['performance']
                mm_perf = analysis['moving_average']['performance']
                
                print(f"  Butterworth - Trades: {butter_perf['total_trades']}, "
                      f"Retorno: {butter_perf['return_percentage']:.2f}%")
                print(f"  Média Móvel - Trades: {mm_perf['total_trades']}, "
                      f"Retorno: {mm_perf['return_percentage']:.2f}%")
                print()
        
        # Criar resumo comparativo
        self.create_summary_report(results)

        return results

    def create_summary_report(self, results):
        """
        Cria relatório resumo comparando as duas estratégias
        """
        # Preparar dados para o resumo
        summary_data = []

        for result in results:
            stock = result['stock']
            butter_perf = result['butterworth']['performance']
            mm_perf = result['moving_average']['performance']

            summary_data.append({
                'Stock': stock,
                'Butterworth_Trades': butter_perf['total_trades'],
                'Butterworth_Return_Pct': butter_perf['return_percentage'],
                'Butterworth_Final_Position': butter_perf['final_position'],
                'MM_Trades': mm_perf['total_trades'],
                'MM_Return_Pct': mm_perf['return_percentage'],
                'MM_Final_Position': mm_perf['final_position'],
                'Better_Strategy': 'Butterworth' if butter_perf['return_percentage'] > mm_perf['return_percentage'] else 'Média Móvel'
            })

        # Criar DataFrame
        summary_df = pd.DataFrame(summary_data)

        # Salvar CSV
        summary_file = self.csv_dir / 'trading_strategy_comparison.csv'
        summary_df.to_csv(summary_file, index=False)

        # Criar gráfico comparativo
        self.plot_summary_comparison(summary_df)

        # Estatísticas gerais
        butter_wins = (summary_df['Better_Strategy'] == 'Butterworth').sum()
        mm_wins = (summary_df['Better_Strategy'] == 'Média Móvel').sum()

        avg_butter_return = summary_df['Butterworth_Return_Pct'].mean()
        avg_mm_return = summary_df['MM_Return_Pct'].mean()

        print("\n" + "=" * 60)
        print("RESUMO COMPARATIVO DAS ESTRATÉGIAS")
        print("=" * 60)
        print(f"Ações analisadas: {len(summary_df)}")
        print(f"Butterworth melhor em: {butter_wins} ações ({butter_wins/len(summary_df)*100:.1f}%)")
        print(f"Média Móvel melhor em: {mm_wins} ações ({mm_wins/len(summary_df)*100:.1f}%)")
        print(f"Retorno médio Butterworth: {avg_butter_return:.2f}%")
        print(f"Retorno médio Média Móvel: {avg_mm_return:.2f}%")
        print(f"\nRelatório salvo em: {summary_file}")

        return summary_df

    def plot_summary_comparison(self, summary_df):
        """
        Cria gráficos de comparação geral
        """
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Comparação Geral das Estratégias de Trading', fontsize=16, fontweight='bold')

        # Gráfico 1: Retornos por ação
        ax1 = axes[0, 0]
        x = range(len(summary_df))
        width = 0.35

        ax1.bar([i - width/2 for i in x], summary_df['Butterworth_Return_Pct'],
                width, label='Butterworth', alpha=0.8)
        ax1.bar([i + width/2 for i in x], summary_df['MM_Return_Pct'],
                width, label='Média Móvel', alpha=0.8)

        ax1.set_xlabel('Ações')
        ax1.set_ylabel('Retorno (%)')
        ax1.set_title('Retorno por Ação')
        ax1.set_xticks(x)
        ax1.set_xticklabels(summary_df['Stock'], rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Gráfico 2: Número de trades por ação
        ax2 = axes[0, 1]
        ax2.bar([i - width/2 for i in x], summary_df['Butterworth_Trades'],
                width, label='Butterworth', alpha=0.8)
        ax2.bar([i + width/2 for i in x], summary_df['MM_Trades'],
                width, label='Média Móvel', alpha=0.8)

        ax2.set_xlabel('Ações')
        ax2.set_ylabel('Número de Trades')
        ax2.set_title('Trades por Ação')
        ax2.set_xticks(x)
        ax2.set_xticklabels(summary_df['Stock'], rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Gráfico 3: Distribuição de retornos
        ax3 = axes[1, 0]
        ax3.hist(summary_df['Butterworth_Return_Pct'], bins=10, alpha=0.7, label='Butterworth', density=True)
        ax3.hist(summary_df['MM_Return_Pct'], bins=10, alpha=0.7, label='Média Móvel', density=True)
        ax3.set_xlabel('Retorno (%)')
        ax3.set_ylabel('Densidade')
        ax3.set_title('Distribuição dos Retornos')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axvline(x=0, color='black', linestyle='--', alpha=0.5)

        # Gráfico 4: Scatter plot retorno vs trades
        ax4 = axes[1, 1]
        ax4.scatter(summary_df['Butterworth_Trades'], summary_df['Butterworth_Return_Pct'],
                   label='Butterworth', alpha=0.7, s=60)
        ax4.scatter(summary_df['MM_Trades'], summary_df['MM_Return_Pct'],
                   label='Média Móvel', alpha=0.7, s=60)
        ax4.set_xlabel('Número de Trades')
        ax4.set_ylabel('Retorno (%)')
        ax4.set_title('Retorno vs Número de Trades')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)

        plt.tight_layout()

        # Salvar gráfico
        filename = self.figures_dir / 'trading_strategy_summary.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Gráfico resumo salvo: {filename}")

if __name__ == "__main__":
    analyzer = TradingStrategyAnalyzer()
    results = analyzer.run_analysis()
